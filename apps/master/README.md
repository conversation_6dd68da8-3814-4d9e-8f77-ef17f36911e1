# 管理节点 (Master Node)

微博舆情分析系统的核心管理节点，基于 Tauri + React + Rust 构建的跨平台桌面应用，负责整个分布式系统的统一管理、监控和数据可视化分析。

## 🎯 核心功能

### 系统管理
- **节点管理**：爬虫节点注册、状态监控、任务分发
- **任务调度**：智能任务分配、优先级管理、失败重试
- **配置管理**：全局配置下发、动态参数调整
- **用户管理**：多用户权限控制、操作审计

### 数据可视化
- **舆情总览**：数据统计、增长趋势、覆盖情况
- **事件分析**：单事件深度分析、传播路径、影响评估
- **主题分布**：热门话题、关键词云、趋势分析
- **用户监测**：用户画像、行为分析、影响力排行

### 实时监控
- **系统监控**：节点状态、资源使用、性能指标
- **任务监控**：执行进度、成功率、错误统计
- **告警管理**：异常检测、实时通知、处理跟踪

## 🏗️ 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端界面层                                │
│              React 18 + TypeScript + TailwindCSS          │
├─────────────────────────────────────────────────────────────┤
│                    桌面应用层                                │
│                      Tauri Framework                       │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层                                │
│                Rust + Tokio + SQLx                        │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层                                │
│           PostgreSQL + Redis + RabbitMQ                   │
└─────────────────────────────────────────────────────────────┘
```

## 🛠️ 技术栈

### 前端技术
- **框架**：React 18 + TypeScript 5+
- **样式**：TailwindCSS 3+ + Shadcn/ui
- **状态管理**：Zustand
- **图表库**：Recharts + D3.js
- **路由**：React Router v6
- **构建工具**：Vite

### 后端技术
- **语言**：Rust 1.70+
- **异步运行时**：Tokio 1.0
- **数据库ORM**：SQLx 0.7
- **序列化**：Serde 1.0
- **日志追踪**：Tracing 0.1

### 桌面应用
- **框架**：Tauri 1.5+
- **打包目标**：Windows (MSI) / Linux (DEB, AppImage) / macOS (DMG)
- **权限控制**：最小权限原则，仅开放必要API

### 数据存储
- **主数据库**：PostgreSQL 15+ (舆情数据、用户数据)
- **缓存**：Redis 7+ (会话、实时数据)
- **消息队列**：RabbitMQ 3.12+ (任务分发、事件通知)

## 📁 项目结构

```
apps/master/
├── src/                          # 前端源码
│   ├── components/               # React组件
│   │   ├── Dashboard/           # 仪表板组件
│   │   ├── EventAnalysis/       # 事件分析组件
│   │   ├── TopicAnalysis/       # 主题分析组件
│   │   ├── UserMonitoring/      # 用户监控组件
│   │   ├── Layout/              # 布局组件
│   │   └── ui/                  # 基础UI组件
│   ├── stores/                  # 状态管理
│   ├── services/                # API服务
│   ├── types/                   # TypeScript类型定义
│   ├── utils/                   # 工具函数
│   └── App.tsx                  # 主应用组件
├── src-tauri/                   # Tauri后端源码
│   ├── src/
│   │   ├── commands/            # Tauri命令处理
│   │   ├── services/            # 业务服务
│   │   ├── models/              # 数据模型
│   │   ├── database/            # 数据库连接
│   │   ├── events/              # 事件处理
│   │   └── main.rs              # 主程序入口
│   ├── Cargo.toml               # Rust依赖配置
│   └── tauri.conf.json          # Tauri配置
├── public/                      # 静态资源
├── package.json                 # Node.js依赖配置
├── tailwind.config.js           # TailwindCSS配置
├── vite.config.ts               # Vite构建配置
└── # 微博舆情分析管理节点

这是微博舆情分析系统的管理节点，基于 Tauri + React + Rust 构建的跨平台桌面应用程序。

## 功能特性

- 🖥️ **节点管理**: 管理分布式爬虫节点，监控节点状态
- 📋 **任务调度**: 智能任务分配和调度，支持优先级管理
- 📊 **数据分析**: 舆情数据可视化分析和报告生成
- 🔍 **系统监控**: 实时系统性能监控和告警管理
- ⚙️ **配置管理**: 全局配置管理和用户权限控制

## 技术栈

### 后端 (Rust)
- **Tauri 1.5+**: 跨平台桌面应用框架
- **Tokio**: 异步运行时
- **SQLx**: PostgreSQL 数据库连接
- **Redis**: 缓存和会话管理
- **RabbitMQ**: 消息队列
- **Serde**: 序列化/反序列化

### 前端 (React)
- **React 18**: 用户界面框架
- **TypeScript**: 类型安全的 JavaScript
- **TailwindCSS**: 实用优先的 CSS 框架
- **Zustand**: 轻量级状态管理
- **Recharts**: 数据可视化图表库
- **Radix UI**: 无障碍 UI 组件库

## 项目结构

```
apps/master/
├── src/                          # 前端源码
│   ├── components/               # React组件
│   │   ├── Dashboard/           # 仪表板组件
│   │   ├── NodeManager/         # 节点管理组件
│   │   ├── TaskManager/         # 任务管理组件
│   │   ├── DataAnalysis/        # 数据分析组件
│   │   ├── SystemMonitor/       # 系统监控组件
│   │   ├── Layout/              # 布局组件
│   │   └── ui/                  # 基础UI组件
│   ├── stores/                  # Zustand状态管理
│   ├── services/                # API服务层
│   ├── types/                   # TypeScript类型定义
│   ├── utils/                   # 工具函数
│   └── hooks/                   # 自定义Hooks
├── src-tauri/                   # Tauri后端源码
│   ├── src/
│   │   ├── commands/            # Tauri命令处理
│   │   ├── services/            # 业务服务层
│   │   ├── models/              # 数据模型
│   │   ├── database/            # 数据库连接
│   │   └── events/              # 事件处理
│   ├── migrations/              # 数据库迁移文件
│   └── Cargo.toml               # Rust依赖配置
├── package.json                 # Node.js依赖配置
└── README.md                    # 项目文档
```

## 环境要求

- **Node.js**: 18.0+
- **Rust**: 1.70+
- **PostgreSQL**: 13+
- **Redis**: 6.0+
- **RabbitMQ**: 3.8+

## 快速开始

### 1. 环境准备

确保已安装以下软件：
- Node.js 和 npm
- Rust 和 Cargo
- PostgreSQL 数据库
- Redis 服务器
- RabbitMQ 消息队列

### 2. 克隆项目

```bash
git clone <repository-url>
cd weibo-sentiment-analysis/apps/master
```

### 3. 配置环境变量

复制环境变量模板并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接等信息：

```env
DATABASE_URL=postgresql://username:password@localhost:5432/weibo_sentiment
REDIS_URL=redis://localhost:6379
RABBITMQ_URL=amqp://guest:guest@localhost:5672
```

### 4. 安装依赖

```bash
# 安装前端依赖
npm install

# Rust 依赖会在构建时自动安装
```

### 5. 数据库初始化

```bash
# 运行数据库迁移
cargo run --bin migrate
```

### 6. 启动开发环境

```bash
# 启动开发服务器
npm run tauri:dev

# 或者使用批处理脚本 (Windows)
start_dev.bat
```

## 可用脚本

- `npm run dev`: 启动前端开发服务器
- `npm run build`: 构建前端生产版本
- `npm run tauri:dev`: 启动 Tauri 开发环境
- `npm run tauri:build`: 构建 Tauri 应用程序
- `npm run test`: 运行测试
- `npm run lint`: 代码检查
- `npm run lint:fix`: 自动修复代码问题

## 核心功能

### 节点管理
- 节点注册和注销
- 实时状态监控
- 配置管理和下发
- 性能统计和分析

### 任务调度
- 任务创建和分配
- 优先级队列管理
- 失败重试机制
- 进度跟踪和监控

### 数据分析
- 舆情数据统计
- 趋势分析图表
- 关键词云图
- 情感分析报告

### 系统监控
- 系统资源监控
- 告警规则配置
- 日志查看和分析
- 性能指标统计

## API 接口

### 节点管理
- `register_node`: 注册新节点
- `get_node_list`: 获取节点列表
- `get_node_detail`: 获取节点详情
- `update_node_config`: 更新节点配置
- `delete_node`: 删除节点

### 任务管理
- `create_task`: 创建新任务
- `get_task_list`: 获取任务列表
- `update_task_status`: 更新任务状态
- `cancel_task`: 取消任务
- `retry_task`: 重试失败任务

### 系统监控
- `get_system_status`: 获取系统状态
- `get_dashboard_data`: 获取仪表板数据
- `get_alerts`: 获取告警信息

## 开发指南

### 添加新功能

1. **后端 (Rust)**:
   - 在 `src-tauri/src/models/` 中定义数据模型
   - 在 `src-tauri/src/services/` 中实现业务逻辑
   - 在 `src-tauri/src/commands/` 中添加 Tauri 命令
   - 在 `src-tauri/src/lib.rs` 中注册新命令

2. **前端 (React)**:
   - 在 `src/types/` 中定义 TypeScript 类型
   - 在 `src/services/` 中添加 API 调用
   - 在 `src/stores/` 中管理状态
   - 在 `src/components/` 中创建 UI 组件

### 代码规范

- 使用 TypeScript 严格模式
- 遵循 ESLint 规则
- 使用 Prettier 格式化代码
- 编写单元测试和集成测试

## 部署

### 开发环境
```bash
npm run tauri:dev
```

### 生产构建
```bash
npm run tauri:build
```

构建产物位于 `src-tauri/target/release/` 目录。

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 PostgreSQL 服务是否启动
   - 验证 `DATABASE_URL` 配置是否正确
   - 确认数据库用户权限

2. **Redis 连接失败**
   - 检查 Redis 服务是否启动
   - 验证 `REDIS_URL` 配置是否正确

3. **RabbitMQ 连接失败**
   - 检查 RabbitMQ 服务是否启动
   - 验证 `RABBITMQ_URL` 配置是否正确

4. **前端构建失败**
   - 清除 node_modules 并重新安装
   - 检查 Node.js 版本是否符合要求

5. **Rust 编译失败**
   - 更新 Rust 工具链
   - 清除 target 目录并重新构建

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。                    # 项目文档
```

## 🚀 快速开始

### 环境要求

- **Rust**: 1.70+ (包含 Cargo)
- **Node.js**: 18+ (包含 npm/yarn)
- **PostgreSQL**: 15+
- **Redis**: 7+
- **RabbitMQ**: 3.12+

### 安装依赖

1. **安装前端依赖**
```bash
cd apps/master
npm install
```

2. **安装Rust依赖**
```bash
cd apps/master/src-tauri
cargo build
```

### 配置环境

1. **创建配置文件**
```bash
cp .env.example .env
```

2. **编辑配置文件**
```bash
# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/weibo_sentiment
REDIS_URL=redis://localhost:6379
RABBITMQ_URL=amqp://admin:password@localhost:5672

# 应用配置
LOG_LEVEL=info

# 外部服务配置
WEIBO_API_KEY=your-weibo-api-key
```

### 数据库初始化

#### 1. 安装SQLx CLI工具

```bash
# 安装SQLx CLI
cargo install sqlx-cli --no-default-features --features postgres,sqlite

# 验证安装
sqlx --version
```

#### 2. 创建数据库

```bash
# 创建PostgreSQL数据库
createdb weibo_sentiment

# 或使用SQL命令
psql -U postgres -c "CREATE DATABASE weibo_sentiment;"
```

#### 3. 运行数据库迁移

```bash
cd apps/master/src-tauri

# 运行所有迁移
sqlx migrate run --database-url postgresql://username:password@localhost:5432/weibo_sentiment

# 或使用环境变量
export DATABASE_URL=postgresql://username:password@localhost:5432/weibo_sentiment
sqlx migrate run
```

#### 4. 导入初始数据（可选）

```bash
# 运行种子数据脚本
cargo run --bin seed
```

### 启动应用

1. **开发模式**
```bash
cd apps/master
npm run tauri dev
```

2. **生产构建**
```bash
cd apps/master
npm run tauri build
```

构建完成后，可执行文件位于 `src-tauri/target/release/bundle/` 目录下。

## 📊 核心功能模块

### 1. 舆情总览面板

**功能特性**：
- 数据库总量统计（微博数、用户数、评论数等）
- 近30天新增数据趋势图
- 爬取覆盖情况可视化
- 当月热门事件TOP5排行

**技术实现**：
- 实时数据聚合查询
- 事件驱动更新机制
- 响应式图表展示
- 缓存优化性能

### 2. 单事件分析面板

**功能特性**：
- 事件基本信息展示
- 传播趋势时间线分析
- 情感分布饼图
- 关键用户影响力分析
- 媒体参与度统计

**技术实现**：
- 复杂SQL聚合查询
- 多维度数据分析
- 交互式图表组件
- 导出分析报告

### 3. 主题分布分析

**功能特性**：
- 热门话题词云展示
- 主题演化趋势分析
- 地域分布热力图
- 相关性网络图谱

**技术实现**：
- NLP主题提取算法
- D3.js复杂可视化
- 地理数据处理
- 网络图布局算法

### 4. 用户监测系统

**功能特性**：
- 重点用户监控列表
- 用户行为轨迹分析
- 影响力评分排行
- 异常行为告警

**技术实现**：
- 用户画像建模
- 行为模式识别
- 实时告警推送
- 数据可视化展示

## 🔧 开发指南

### Tauri命令接口

管理节点通过Tauri命令与前端交互：

**数据查询命令**：
```rust
#[tauri::command]
async fn get_dashboard_overview() -> Result<DashboardData, String>

#[tauri::command]
async fn get_event_details(event_id: String) -> Result<EventData, String>

#[tauri::command]
async fn get_topic_distribution() -> Result<TopicData, String>

#[tauri::command]
async fn get_user_monitoring() -> Result<UserMonitoringData, String>
```

**系统管理命令**：
```rust
#[tauri::command]
async fn create_crawl_task(task_config: TaskConfig) -> Result<String, String>

#[tauri::command]
async fn update_node_config(node_id: String, config: NodeConfig) -> Result<(), String>

#[tauri::command]
async fn get_system_status() -> Result<SystemStatus, String>
```

### 状态管理

使用Zustand进行全局状态管理：

```typescript
// 系统状态
interface SystemState {
  theme: 'light' | 'dark';
  sidebarCollapsed: boolean;
  currentUser: User | null;
  notifications: Notification[];
}

// 数据状态
interface DataState {
  dashboardData: DashboardData | null;
  eventData: EventData | null;
  topicData: TopicData | null;
  userMonitoringData: UserMonitoringData | null;
}
```

### 组件开发规范

1. **组件命名**：使用PascalCase，文件名与组件名一致
2. **Props接口**：定义清晰的TypeScript接口
3. **样式规范**：使用TailwindCSS类名，避免内联样式
4. **错误处理**：使用ErrorBoundary包装组件
5. **性能优化**：合理使用React.memo和useMemo

### 数据库设计

**核心表结构**：
- `users`: 用户信息
- `posts`: 微博内容数据（按时间分区）
- `comments`: 评论数据
- `sentiment_analysis`: 情感分析结果
- `events`: 舆情事件
- `topics`: 主题标签
- `crawler_nodes`: 爬虫节点信息
- `crawl_tasks`: 爬取任务
- `user_profiles`: 用户画像数据

## �️ 数据库迁移管理

### 迁移文件结构

```
src-tauri/migrations/
├── 20240101000001_create_users_table.sql
├── 20240101000002_create_posts_table.sql
├── 20240101000003_create_comments_table.sql
├── 20240101000004_create_sentiment_analysis_table.sql
├── 20240101000005_create_events_table.sql
├── 20240101000006_create_topics_table.sql
├── 20240101000007_create_crawler_nodes_table.sql
├── 20240101000008_create_crawl_tasks_table.sql
├── 20240101000009_create_user_profiles_table.sql
├── 20240101000010_create_indexes.sql
└── 20240101000011_create_partitions.sql
```

### 核心迁移文件

#### 1. 用户表 (20240101000001_create_users_table.sql)

```sql
-- 用户基础信息表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    weibo_uid VARCHAR(50) UNIQUE NOT NULL,
    username VARCHAR(100) NOT NULL,
    nickname VARCHAR(100),
    avatar_url TEXT,
    verified_type INTEGER DEFAULT 0, -- 0:未认证 1:个人认证 2:企业认证
    followers_count INTEGER DEFAULT 0,
    following_count INTEGER DEFAULT 0,
    posts_count INTEGER DEFAULT 0,
    location VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_users_weibo_uid ON users(weibo_uid);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_verified_type ON users(verified_type);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### 2. 微博内容表 (20240101000002_create_posts_table.sql)

```sql
-- 微博内容表（分区表）
CREATE TABLE posts (
    id BIGSERIAL,
    weibo_id VARCHAR(50) UNIQUE NOT NULL,
    user_id BIGINT REFERENCES users(id),
    content TEXT NOT NULL,
    content_type INTEGER DEFAULT 0, -- 0:原创 1:转发 2:评论
    parent_id BIGINT,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    repost_count INTEGER DEFAULT 0,
    published_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (id, published_at)
) PARTITION BY RANGE (published_at);

-- 创建索引
CREATE INDEX idx_posts_weibo_id ON posts(weibo_id);
CREATE INDEX idx_posts_user_id ON posts(user_id);
CREATE INDEX idx_posts_content_type ON posts(content_type);
CREATE INDEX idx_posts_published_at ON posts(published_at);
CREATE INDEX idx_posts_parent_id ON posts(parent_id) WHERE parent_id IS NOT NULL;

-- 全文搜索索引
CREATE INDEX idx_posts_content_fts ON posts USING gin(to_tsvector('chinese', content));

-- 创建更新时间触发器
CREATE TRIGGER update_posts_updated_at
    BEFORE UPDATE ON posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### 3. 情感分析表 (20240101000004_create_sentiment_analysis_table.sql)

```sql
-- 情感分析结果表
CREATE TABLE sentiment_analysis (
    id BIGSERIAL PRIMARY KEY,
    post_id BIGINT NOT NULL,
    sentiment_score DECIMAL(3,2) NOT NULL, -- -1.00 到 1.00
    emotion_type INTEGER NOT NULL, -- 1:愤怒 2:厌恶 3:恐惧 4:快乐 5:悲伤 6:惊讶 7:信任 8:期待
    confidence DECIMAL(3,2) NOT NULL, -- 0.00 到 1.00
    keywords TEXT[], -- 关键词数组
    analyzed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_sentiment_post_id ON sentiment_analysis(post_id);
CREATE INDEX idx_sentiment_emotion_type ON sentiment_analysis(emotion_type);
CREATE INDEX idx_sentiment_score ON sentiment_analysis(sentiment_score);
CREATE INDEX idx_sentiment_analyzed_at ON sentiment_analysis(analyzed_at);

-- 创建复合索引用于统计查询
CREATE INDEX idx_sentiment_emotion_score ON sentiment_analysis(emotion_type, sentiment_score);
```

#### 4. 舆情事件表 (20240101000005_create_events_table.sql)

```sql
-- 舆情事件表
CREATE TABLE events (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    keywords TEXT[] NOT NULL, -- 关键词数组
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    status INTEGER DEFAULT 1, -- 1:进行中 2:已结束 3:已归档
    total_posts INTEGER DEFAULT 0,
    total_interactions INTEGER DEFAULT 0,
    heat_score DECIMAL(5,2) DEFAULT 0.00, -- 热度评分
    created_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_events_status ON events(status);
CREATE INDEX idx_events_start_time ON events(start_time);
CREATE INDEX idx_events_heat_score ON events(heat_score DESC);
CREATE INDEX idx_events_keywords ON events USING gin(keywords);

-- 创建更新时间触发器
CREATE TRIGGER update_events_updated_at
    BEFORE UPDATE ON events
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### 5. 爬虫节点表 (20240101000007_create_crawler_nodes_table.sql)

```sql
-- 爬虫节点信息表
CREATE TABLE crawler_nodes (
    id BIGSERIAL PRIMARY KEY,
    node_id VARCHAR(50) UNIQUE NOT NULL,
    node_name VARCHAR(100) NOT NULL,
    node_type INTEGER DEFAULT 1, -- 1:爬虫节点 2:管理节点
    ip_address INET,
    port INTEGER,
    status INTEGER DEFAULT 1, -- 1:在线 2:离线 3:维护中
    cpu_usage DECIMAL(5,2) DEFAULT 0.00,
    memory_usage DECIMAL(5,2) DEFAULT 0.00,
    disk_usage DECIMAL(5,2) DEFAULT 0.00,
    active_tasks INTEGER DEFAULT 0,
    total_tasks INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0.00,
    last_heartbeat TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_nodes_node_id ON crawler_nodes(node_id);
CREATE INDEX idx_nodes_status ON crawler_nodes(status);
CREATE INDEX idx_nodes_last_heartbeat ON crawler_nodes(last_heartbeat);
CREATE INDEX idx_nodes_node_type ON crawler_nodes(node_type);

-- 创建更新时间触发器
CREATE TRIGGER update_crawler_nodes_updated_at
    BEFORE UPDATE ON crawler_nodes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 分区管理

#### 创建分区 (20240101000011_create_partitions.sql)

```sql
-- 为posts表创建月度分区
-- 当前月份分区
CREATE TABLE posts_current PARTITION OF posts
FOR VALUES FROM (date_trunc('month', CURRENT_DATE))
TO (date_trunc('month', CURRENT_DATE) + INTERVAL '1 month');

-- 下个月分区
CREATE TABLE posts_next PARTITION OF posts
FOR VALUES FROM (date_trunc('month', CURRENT_DATE) + INTERVAL '1 month')
TO (date_trunc('month', CURRENT_DATE) + INTERVAL '2 months');

-- 创建分区管理函数
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name TEXT, start_date DATE)
RETURNS VOID AS $$
DECLARE
    partition_name TEXT;
    start_month DATE;
    end_month DATE;
BEGIN
    start_month := date_trunc('month', start_date);
    end_month := start_month + INTERVAL '1 month';
    partition_name := table_name || '_' || to_char(start_month, 'YYYY_MM');

    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF %I
                    FOR VALUES FROM (%L) TO (%L)',
                   partition_name, table_name, start_month, end_month);
END;
$$ LANGUAGE plpgsql;

-- 创建自动分区管理存储过程
CREATE OR REPLACE FUNCTION maintain_partitions()
RETURNS VOID AS $$
DECLARE
    current_month DATE;
    next_month DATE;
BEGIN
    current_month := date_trunc('month', CURRENT_DATE);
    next_month := current_month + INTERVAL '1 month';

    -- 创建下个月的分区
    PERFORM create_monthly_partition('posts', next_month);

    -- 删除6个月前的分区（可选）
    -- EXECUTE format('DROP TABLE IF EXISTS posts_%s',
    --                to_char(current_month - INTERVAL '6 months', 'YYYY_MM'));
END;
$$ LANGUAGE plpgsql;
```

### 迁移管理命令

#### 常用SQLx命令

```bash
# 创建新的迁移文件
sqlx migrate add create_new_table

# 运行所有待执行的迁移
sqlx migrate run

# 回滚最后一次迁移
sqlx migrate revert

# 查看迁移状态
sqlx migrate info

# 强制设置迁移版本（谨慎使用）
sqlx migrate force <version>

# 验证迁移文件
sqlx migrate validate
```

#### 自定义迁移脚本

```bash
#!/bin/bash
# scripts/migrate.sh

set -e

DATABASE_URL=${DATABASE_URL:-"postgresql://postgres:password@localhost:5432/weibo_sentiment"}

echo "开始数据库迁移..."

# 检查数据库连接
if ! pg_isready -d "$DATABASE_URL" > /dev/null 2>&1; then
    echo "错误: 无法连接到数据库"
    exit 1
fi

# 运行迁移
sqlx migrate run --database-url "$DATABASE_URL"

# 创建分区
echo "创建分区..."
psql "$DATABASE_URL" -c "SELECT maintain_partitions();"

echo "数据库迁移完成!"
```

### 数据库备份与恢复

#### 备份脚本

```bash
#!/bin/bash
# scripts/backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backups/weibo_sentiment"
DATABASE_URL="postgresql://postgres:password@localhost:5432/weibo_sentiment"

mkdir -p "$BACKUP_DIR"

echo "开始备份数据库..."

# 全量备份
pg_dump "$DATABASE_URL" > "$BACKUP_DIR/full_backup_$DATE.sql"

# 仅结构备份
pg_dump --schema-only "$DATABASE_URL" > "$BACKUP_DIR/schema_backup_$DATE.sql"

# 仅数据备份
pg_dump --data-only "$DATABASE_URL" > "$BACKUP_DIR/data_backup_$DATE.sql"

# 压缩备份文件
gzip "$BACKUP_DIR"/*_$DATE.sql

echo "备份完成: $BACKUP_DIR"
```

#### 恢复脚本

```bash
#!/bin/bash
# scripts/restore.sh

if [ $# -ne 1 ]; then
    echo "用法: $0 <backup_file>"
    exit 1
fi

BACKUP_FILE=$1
DATABASE_URL="postgresql://postgres:password@localhost:5432/weibo_sentiment"

echo "开始恢复数据库..."

# 如果是压缩文件，先解压
if [[ $BACKUP_FILE == *.gz ]]; then
    gunzip -c "$BACKUP_FILE" | psql "$DATABASE_URL"
else
    psql "$DATABASE_URL" < "$BACKUP_FILE"
fi

echo "数据库恢复完成!"
```

## �🔍 监控与运维

### 系统监控

**监控指标**：
- 节点状态：在线数量、CPU/内存使用率
- 任务执行：成功率、平均耗时、错误统计
- 数据质量：采集量、处理延迟、异常数据比例
- 系统性能：数据查询响应时间、数据库连接数、缓存命中率

**告警规则**：
- 节点离线超过5分钟
- 任务失败率超过10%
- 数据处理延迟超过10分钟
- 系统资源使用率超过80%

### 日志管理

使用结构化日志记录：

```rust
// 操作日志
tracing::info!(
    user_id = %user.id,
    operation = %operation_name,
    duration_ms = %duration.as_millis(),
    "User operation completed"
);

// 错误日志
tracing::error!(
    error = %e,
    task_id = %task.id,
    node_id = %node.id,
    "Task execution failed"
);
```

### 性能优化

**数据库优化**：
- 合理创建索引
- 使用连接池
- 查询结果缓存
- 分页查询大数据集

**前端优化**：
- 组件懒加载
- 图表数据虚拟化
- 防抖节流处理
- 资源预加载

## 🧪 测试

### 单元测试

```bash
# 前端测试
npm run test

# 后端测试
cd src-tauri
cargo test
```

### 集成测试

```bash
# 启动测试环境
docker-compose -f docker-compose.test.yml up -d

# 运行集成测试
npm run test:integration
```

### E2E测试

```bash
# 安装Playwright
npm install @playwright/test

# 运行E2E测试
npm run test:e2e
```

## 📦 部署

### 开发环境部署

```bash
# 启动依赖服务
docker-compose up -d postgres redis rabbitmq

# 启动开发服务器
npm run tauri dev
```

### 生产环境部署

```bash
# 构建应用
npm run tauri build

# 生成安装包
# Windows: target/release/bundle/msi/
# Linux: target/release/bundle/deb/ 和 target/release/bundle/appimage/
# macOS: target/release/bundle/dmg/
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 代码规范

- **Rust**: 使用 `cargo fmt` 和 `cargo clippy`
- **TypeScript**: 使用 ESLint 和 Prettier
- **提交信息**: 遵循 Conventional Commits 规范

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../../LICENSE) 文件了解详情。

## 🆘 故障排除

### 常见问题

**Q: 应用启动失败，提示数据库连接错误**
A: 检查PostgreSQL服务是否启动，配置文件中的数据库连接信息是否正确。

**Q: 前端页面空白，控制台报错**
A: 检查Node.js版本是否符合要求，尝试删除node_modules重新安装依赖。

**Q: 构建失败，提示Rust编译错误**
A: 检查Rust版本是否符合要求，确保所有系统依赖已安装。

**Q: WebSocket连接失败**
A: 检查防火墙设置，确保WebSocket端口未被阻塞。

### 获取帮助

- 📖 查看[项目文档](../../docs/)
- 🐛 提交[Issue](https://github.com/your-org/weibo-sentiment-analysis/issues)
- 💬 加入[讨论区](https://github.com/your-org/weibo-sentiment-analysis/discussions)
- 📧 联系维护者：<EMAIL>